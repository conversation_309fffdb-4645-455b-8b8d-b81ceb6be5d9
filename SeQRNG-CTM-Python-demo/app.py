#!/usr/bin/env python3
"""
SeQRNG-CTM REST API
A Flask REST API for integrating SeQRNG quantum random number generator with Thales CipherTrust Manager
"""

from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import os
import sys
import traceback
from datetime import datetime

# Import existing modules
from interface_seqrng_v2 import sq_get_random_bytes, get_random_hex_key, get_random_alphanumeric_key
from sq_ctm_modules_v1 import ctm_upload_key, ctm_get_api_key, ctm_key_exists, check_key_length
from config import get_config

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configuration
app.config['JSON_SORT_KEYS'] = False

# Swagger UI HTML template (using CDN)
SWAGGER_UI_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>SeQRNG-CTM REST API - Swagger UI</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin:0;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: '/swagger.yaml',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout"
            });
        };
    </script>
</body>
</html>
"""

# Global configuration variables
seqrng_config = None
ctm_config = None

def load_api_configuration():
    """
    Load configuration for API mode (non-interactive)
    """
    global seqrng_config, ctm_config
    
    try:
        config = get_config()
        
        # Validate configuration
        if not config.validate_config():
            raise Exception("Configuration validation failed. Please check your .env file or environment variables.")
        
        # Get configuration
        seqrng_config = config.get_seqrng_config()
        ctm_config = config.get_ctm_config()
        
        return True
        
    except Exception as e:
        print(f"Configuration error: {e}")
        return False

def create_error_response(message, status_code=400, error_type="error"):
    """Create standardized error response"""
    return jsonify({
        "status": "error",
        "error_type": error_type,
        "message": message,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }), status_code

def create_success_response(data, message="Success"):
    """Create standardized success response"""
    return jsonify({
        "status": "success",
        "message": message,
        "data": data,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    })

def validate_json_request():
    """Validate that request contains valid JSON"""
    if not request.is_json:
        return create_error_response("Request must contain valid JSON", 400, "validation_error")
    return None

def validate_key_name(key_name):
    """Validate key name format"""
    if not key_name or not isinstance(key_name, str):
        return "Key name must be a non-empty string"
    if len(key_name) > 100:
        return "Key name must be 100 characters or less"
    if not key_name.replace('_', '').replace('-', '').isalnum():
        return "Key name can only contain alphanumeric characters, underscores, and hyphens"
    return None

@app.errorhandler(404)
def not_found(error):
    return create_error_response("Endpoint not found", 404, "not_found")

@app.errorhandler(405)
def method_not_allowed(error):
    return create_error_response("Method not allowed", 405, "method_not_allowed")

@app.errorhandler(500)
def internal_error(error):
    return create_error_response("Internal server error", 500, "internal_error")

@app.route('/docs')
def swagger_ui():
    """Serve Swagger UI"""
    return render_template_string(SWAGGER_UI_HTML)

@app.route('/swagger.yaml')
def swagger_spec():
    """Serve the swagger.yaml file"""
    try:
        with open('swagger.yaml', 'r') as f:
            swagger_content = f.read()
        return swagger_content, 200, {'Content-Type': 'application/x-yaml'}
    except FileNotFoundError:
        return create_error_response("Swagger specification not found", 404, "not_found")

@app.route('/api/v1/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Check if configuration is loaded
        config_status = seqrng_config is not None and ctm_config is not None

        return create_success_response({
            "service": "SeQRNG-CTM API",
            "version": "1.0.0",
            "status": "healthy",
            "configuration_loaded": config_status,
            "endpoints": {
                "key_generation": "/api/v1/keys/generate/*",
                "ctm_management": "/api/v1/ctm/*",
                "health": "/api/v1/health",
                "config": "/api/v1/config/status",
                "docs": "/api/v1/docs"
            }
        })
    except Exception as e:
        return create_error_response(f"Health check failed: {str(e)}", 500)

@app.route('/api/v1/docs', methods=['GET'])
def api_documentation():
    """API documentation endpoint"""
    docs = {
        "title": "SeQRNG-CTM REST API",
        "version": "1.0.0",
        "description": "REST API for integrating SeQRNG quantum random number generator with Thales CipherTrust Manager",
        "base_url": request.base_url.replace('/api/v1/docs', ''),
        "endpoints": {
            "health_check": {
                "method": "GET",
                "path": "/api/v1/health",
                "description": "Check API health status"
            },
            "config_status": {
                "method": "GET",
                "path": "/api/v1/config/status",
                "description": "Get configuration status"
            },
            "generate_bytes": {
                "method": "POST",
                "path": "/api/v1/keys/generate/bytes",
                "description": "Generate random bytes from SeQRNG",
                "parameters": {
                    "num_bytes": "integer (1-1024, default: 32)",
                    "packages": "integer (1-100, default: 1)"
                }
            },
            "generate_hex": {
                "method": "POST",
                "path": "/api/v1/keys/generate/hex",
                "description": "Generate hexadecimal key from SeQRNG",
                "parameters": {
                    "num_bytes": "integer (1-1024, default: 32)"
                }
            },
            "generate_alphanumeric": {
                "method": "POST",
                "path": "/api/v1/keys/generate/alphanumeric",
                "description": "Generate alphanumeric key from SeQRNG",
                "parameters": {
                    "num_bytes": "integer (1-1024, default: 32)"
                }
            },
            "ctm_get_token": {
                "method": "GET",
                "path": "/api/v1/ctm/auth/token",
                "description": "Get CTM authentication token"
            },
            "ctm_check_key": {
                "method": "GET",
                "path": "/api/v1/ctm/keys/{key_name}/exists",
                "description": "Check if key exists in CTM"
            },
            "ctm_upload_key": {
                "method": "POST",
                "path": "/api/v1/ctm/keys/upload",
                "description": "Upload single key to CTM",
                "parameters": {
                    "key_name": "string (required)",
                    "algorithm": "string (AES|RSA|HMAC, default: AES)",
                    "num_bytes": "integer (1-1024, default: 32)",
                    "owner": "string (default: api_user)",
                    "exportable": "boolean (default: false)",
                    "key_material_base64": "string (optional, generates new if not provided)"
                }
            },
            "ctm_upload_batch": {
                "method": "POST",
                "path": "/api/v1/ctm/keys/upload/batch",
                "description": "Upload multiple keys to CTM",
                "parameters": {
                    "key_name_prefix": "string (required)",
                    "algorithm": "string (AES|RSA|HMAC, default: AES)",
                    "num_bytes": "integer (1-1024, default: 32)",
                    "owner": "string (default: api_user)",
                    "exportable": "boolean (default: false)",
                    "key_count": "integer (1-100, default: 1)",
                    "key_num_start": "integer (default: 1)"
                }
            }
        },
        "response_format": {
            "success": {
                "status": "success",
                "message": "string",
                "data": "object",
                "timestamp": "ISO 8601 string"
            },
            "error": {
                "status": "error",
                "error_type": "string",
                "message": "string",
                "timestamp": "ISO 8601 string"
            }
        }
    }

    return create_success_response(docs, "API documentation retrieved successfully")

@app.route('/api/v1/config/status', methods=['GET'])
def config_status():
    """Get configuration status"""
    try:
        config_loaded = seqrng_config is not None and ctm_config is not None
        
        status_data = {
            "configuration_loaded": config_loaded
        }
        
        if config_loaded:
            status_data.update({
                "seqrng_url": seqrng_config['base_url'],
                "ctm_url": ctm_config['base_url'],
                "ctm_domain": ctm_config['domain']
            })
        
        return create_success_response(status_data)
    except Exception as e:
        return create_error_response(f"Failed to get configuration status: {str(e)}", 500)

# Key Generation Endpoints

@app.route('/api/v1/keys/generate/bytes', methods=['POST'])
def generate_random_bytes():
    """Generate random bytes from SeQRNG"""
    try:
        if not seqrng_config:
            return create_error_response("SeQRNG configuration not loaded", 500, "configuration_error")

        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)
        packages = data.get('packages', 1)

        # Validate parameters
        if not isinstance(num_bytes, int) or num_bytes < 1 or num_bytes > 1024:
            return create_error_response("num_bytes must be an integer between 1 and 1024", 400, "validation_error")

        if not isinstance(packages, int) or packages < 1 or packages > 100:
            return create_error_response("packages must be an integer between 1 and 100", 400, "validation_error")

        # Generate random bytes
        random_bytes, error_str, entropy_str, entropy_status = sq_get_random_bytes(
            num_bytes, packages, seqrng_config['base_url'], seqrng_config['api_token']
        )

        # Convert bytes to base64 for JSON response
        import base64
        random_bytes_b64 = base64.b64encode(random_bytes).decode('utf-8')

        response_data = {
            "random_bytes_base64": random_bytes_b64,
            "num_bytes": num_bytes,
            "packages": packages,
            "entropy_report": {
                "error_string": error_str,
                "entropy_string": entropy_str,
                "entropy_status": entropy_status
            }
        }

        return create_success_response(response_data, "Random bytes generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate random bytes: {str(e)}", 500, "generation_error")

@app.route('/api/v1/keys/generate/hex', methods=['POST'])
def generate_hex_key():
    """Generate random hexadecimal key from SeQRNG"""
    try:
        if not seqrng_config:
            return create_error_response("SeQRNG configuration not loaded", 500, "configuration_error")

        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)

        # Validate parameters
        if not isinstance(num_bytes, int) or num_bytes < 1 or num_bytes > 1024:
            return create_error_response("num_bytes must be an integer between 1 and 1024", 400, "validation_error")

        # Generate hex key
        hex_key, error_str, entropy_str, entropy_status = get_random_hex_key(
            num_bytes, seqrng_config['base_url'], seqrng_config['api_token']
        )

        response_data = {
            "hex_key": hex_key,
            "num_bytes": num_bytes,
            "entropy_report": {
                "error_string": error_str,
                "entropy_string": entropy_str,
                "entropy_status": entropy_status
            }
        }

        return create_success_response(response_data, "Hex key generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate hex key: {str(e)}", 500, "generation_error")

@app.route('/api/v1/keys/generate/alphanumeric', methods=['POST'])
def generate_alphanumeric_key():
    """Generate random alphanumeric key from SeQRNG"""
    try:
        if not seqrng_config:
            return create_error_response("SeQRNG configuration not loaded", 500, "configuration_error")

        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)

        # Validate parameters
        if not isinstance(num_bytes, int) or num_bytes < 1 or num_bytes > 1024:
            return create_error_response("num_bytes must be an integer between 1 and 1024", 400, "validation_error")

        # Generate alphanumeric key
        alphanumeric_key, error_str, entropy_str, entropy_status = get_random_alphanumeric_key(
            num_bytes, seqrng_config['base_url'], seqrng_config['api_token']
        )

        response_data = {
            "alphanumeric_key": alphanumeric_key,
            "num_bytes": num_bytes,
            "entropy_report": {
                "error_string": error_str,
                "entropy_string": entropy_str,
                "entropy_status": entropy_status
            }
        }

        return create_success_response(response_data, "Alphanumeric key generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate alphanumeric key: {str(e)}", 500, "generation_error")

# CTM Management Endpoints

@app.route('/api/v1/ctm/auth/token', methods=['GET'])
def get_ctm_token():
    """Get CTM API token"""
    try:
        if not ctm_config:
            return create_error_response("CTM configuration not loaded", 500, "configuration_error")

        # Get CTM API token
        api_key = ctm_get_api_key(
            ctm_config['base_url'],
            ctm_config['username'],
            ctm_config['password'],
            ctm_config['domain']
        )

        response_data = {
            "token_obtained": True,
            "ctm_url": ctm_config['base_url']
        }

        return create_success_response(response_data, "CTM token obtained successfully")

    except Exception as e:
        return create_error_response(f"Failed to get CTM token: {str(e)}", 500, "authentication_error")

@app.route('/api/v1/ctm/keys/<key_name>/exists', methods=['GET'])
def check_key_exists(key_name):
    """Check if a key exists in CTM"""
    try:
        if not ctm_config:
            return create_error_response("CTM configuration not loaded", 500, "configuration_error")

        # Get CTM API token
        api_key = ctm_get_api_key(
            ctm_config['base_url'],
            ctm_config['username'],
            ctm_config['password'],
            ctm_config['domain']
        )

        # Check if key exists
        exists = ctm_key_exists(ctm_config['base_url'], api_key, key_name)

        response_data = {
            "key_name": key_name,
            "exists": exists
        }

        return create_success_response(response_data, f"Key existence check completed")

    except Exception as e:
        return create_error_response(f"Failed to check key existence: {str(e)}", 500, "check_error")

@app.route('/api/v1/ctm/keys/upload', methods=['POST'])
def upload_key_to_ctm():
    """Upload a single key to CTM"""
    try:
        if not ctm_config or not seqrng_config:
            return create_error_response("Configuration not loaded", 500, "configuration_error")

        # Validate JSON request
        json_error = validate_json_request()
        if json_error:
            return json_error

        # Get request parameters
        data = request.get_json() or {}

        # Required parameters
        key_name = data.get('key_name')
        algorithm = data.get('algorithm', 'AES')
        num_bytes = data.get('num_bytes', 32)
        owner = data.get('owner', 'api_user')
        exportable = data.get('exportable', False)

        # Optional: provide existing key material or generate new
        key_material_b64 = data.get('key_material_base64')

        # Validate key name
        key_name_error = validate_key_name(key_name)
        if key_name_error:
            return create_error_response(key_name_error, 400, "validation_error")

        if algorithm not in ['AES', 'RSA', 'HMAC']:
            return create_error_response("algorithm must be one of: AES, RSA, HMAC", 400, "validation_error")

        if not isinstance(num_bytes, int) or num_bytes < 1 or num_bytes > 1024:
            return create_error_response("num_bytes must be an integer between 1 and 1024", 400, "validation_error")

        # Check key length for algorithm
        if not check_key_length(algorithm, num_bytes):
            return create_error_response(f"Invalid key length {num_bytes} for algorithm {algorithm}", 400, "validation_error")

        # Get CTM API token
        api_key = ctm_get_api_key(
            ctm_config['base_url'],
            ctm_config['username'],
            ctm_config['password'],
            ctm_config['domain']
        )

        # Check if key already exists
        if ctm_key_exists(ctm_config['base_url'], api_key, key_name):
            return create_error_response(f"Key '{key_name}' already exists in CTM", 409, "conflict_error")

        # Get key material
        if key_material_b64:
            # Use provided key material
            import base64
            key_material = base64.b64decode(key_material_b64)
            entropy_report = {"source": "provided", "error_string": "N/A", "entropy_string": "N/A", "entropy_status": "N/A"}
        else:
            # Generate new key material from SeQRNG
            key_material, error_str, entropy_str, entropy_status = sq_get_random_bytes(
                num_bytes, 1, seqrng_config['base_url'], seqrng_config['api_token']
            )
            entropy_report = {
                "source": "seqrng",
                "error_string": error_str,
                "entropy_string": entropy_str,
                "entropy_status": entropy_status
            }

        # Upload key to CTM
        upload_result = ctm_upload_key(
            ctm_config['base_url'],
            api_key,
            key_name,
            key_material,
            algorithm,
            owner,
            not exportable  # CTM expects unexportable flag
        )

        response_data = {
            "key_name": key_name,
            "algorithm": algorithm,
            "num_bytes": num_bytes,
            "owner": owner,
            "exportable": exportable,
            "upload_successful": True,
            "entropy_report": entropy_report
        }

        return create_success_response(response_data, "Key uploaded to CTM successfully")

    except Exception as e:
        return create_error_response(f"Failed to upload key to CTM: {str(e)}", 500, "upload_error")

@app.route('/api/v1/ctm/keys/upload/batch', methods=['POST'])
def upload_keys_batch_to_ctm():
    """Upload multiple keys to CTM"""
    try:
        if not ctm_config or not seqrng_config:
            return create_error_response("Configuration not loaded", 500, "configuration_error")

        # Validate JSON request
        json_error = validate_json_request()
        if json_error:
            return json_error

        # Get request parameters
        data = request.get_json() or {}

        # Required parameters
        key_name_prefix = data.get('key_name_prefix')
        algorithm = data.get('algorithm', 'AES')
        num_bytes = data.get('num_bytes', 32)
        owner = data.get('owner', 'api_user')
        exportable = data.get('exportable', False)
        key_count = data.get('key_count', 1)
        key_num_start = data.get('key_num_start', 1)

        # Validate key name prefix
        key_name_error = validate_key_name(key_name_prefix)
        if key_name_error:
            return create_error_response(f"key_name_prefix: {key_name_error}", 400, "validation_error")

        if algorithm not in ['AES', 'RSA', 'HMAC']:
            return create_error_response("algorithm must be one of: AES, RSA, HMAC", 400, "validation_error")

        if not isinstance(num_bytes, int) or num_bytes < 1 or num_bytes > 1024:
            return create_error_response("num_bytes must be an integer between 1 and 1024", 400, "validation_error")

        if not isinstance(key_count, int) or key_count < 1 or key_count > 100:
            return create_error_response("key_count must be an integer between 1 and 100", 400, "validation_error")

        # Check key length for algorithm
        if not check_key_length(algorithm, num_bytes):
            return create_error_response(f"Invalid key length {num_bytes} for algorithm {algorithm}", 400, "validation_error")

        # Get CTM API token
        api_key = ctm_get_api_key(
            ctm_config['base_url'],
            ctm_config['username'],
            ctm_config['password'],
            ctm_config['domain']
        )

        # Generate multiple keys from SeQRNG
        key_materials, error_str, entropy_str, entropy_status = sq_get_random_bytes(
            num_bytes, key_count, seqrng_config['base_url'], seqrng_config['api_token']
        )

        # Split the key materials into individual keys
        from sq_ctm_modules_v1 import group_bytes
        individual_keys = group_bytes(key_materials, num_bytes)

        # Upload each key
        uploaded_keys = []
        failed_keys = []

        for i, key_material in enumerate(individual_keys):
            key_name = f"{key_name_prefix}_{key_num_start + i:04d}"

            try:
                # Check if key already exists
                if ctm_key_exists(ctm_config['base_url'], api_key, key_name):
                    failed_keys.append({
                        "key_name": key_name,
                        "error": "Key already exists"
                    })
                    continue

                # Upload key to CTM
                upload_result = ctm_upload_key(
                    ctm_config['base_url'],
                    api_key,
                    key_name,
                    key_material,
                    algorithm,
                    owner,
                    not exportable  # CTM expects unexportable flag
                )

                uploaded_keys.append({
                    "key_name": key_name,
                    "algorithm": algorithm,
                    "num_bytes": num_bytes
                })

            except Exception as e:
                failed_keys.append({
                    "key_name": key_name,
                    "error": str(e)
                })

        response_data = {
            "key_name_prefix": key_name_prefix,
            "algorithm": algorithm,
            "num_bytes": num_bytes,
            "owner": owner,
            "exportable": exportable,
            "requested_count": key_count,
            "uploaded_count": len(uploaded_keys),
            "failed_count": len(failed_keys),
            "uploaded_keys": uploaded_keys,
            "failed_keys": failed_keys,
            "entropy_report": {
                "error_string": error_str,
                "entropy_string": entropy_str,
                "entropy_status": entropy_status
            }
        }

        if len(uploaded_keys) > 0:
            return create_success_response(response_data, f"Batch upload completed: {len(uploaded_keys)} keys uploaded, {len(failed_keys)} failed")
        else:
            return create_error_response("No keys were uploaded successfully", 400, "batch_upload_error")

    except Exception as e:
        return create_error_response(f"Failed to upload keys batch to CTM: {str(e)}", 500, "batch_upload_error")

# Initialize configuration on startup
def initialize_app():
    """Initialize the application"""
    global seqrng_config, ctm_config
    if not load_api_configuration():
        print("Warning: Failed to load configuration. API will not function properly.")
        return False
    return True

# Initialize configuration when module is imported
with app.app_context():
    initialize_app()

if __name__ == '__main__':
    # Load configuration
    if not load_api_configuration():
        print("Error: Failed to load configuration. Exiting.")
        sys.exit(1)
    
    print("✅ Configuration loaded successfully")
    print(f"📡 SeQRNG: {seqrng_config['base_url']}")
    print(f"🔐 CTM: {ctm_config['base_url']}")
    print("🚀 Starting SeQRNG-CTM REST API...")
    
    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000)),
        debug=os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    )
