# SeQRNG-CTM Integration

🔐 **Secure Production-Ready Integration** between <PERSON><PERSON><PERSON><PERSON> quantum random number generator and Thales CipherTrust Manager (CTM).

✨ **Enhanced Connectivity**: Now supports IP addresses, hostnames, and full URLs with automatic detection and backward compatibility.

## 🚀 Quick Start

### Option 1: Automated Installation (Recommended)
```bash
chmod +x install.sh
sudo ./install.sh
```

### Option 2: Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Configure credentials (interactive)
python config.py --setup

# Run the application
python SeQRNG_CTM_1.4.py
```

## 🔐 Security Features

✅ **No hardcoded credentials** - All sensitive data externalized  
✅ **Multiple configuration methods** - Environment variables, .env files, config files  
✅ **Configuration validation** - Automatic validation of required settings  
✅ **Secure defaults** - Production-ready security configurations  
✅ **Comprehensive deployment guide** - Step-by-step security hardening  

## 📋 Configuration Options

The application supports multiple secure configuration methods:

1. **Environment Variables** (highest priority)
2. **.env File** (recommended for production)
3. **JSON Configuration File**
4. **Interactive Setup**

### 🌐 Connection Formats Supported

Both SeQRNG and CTM connections now support **flexible address formats**:

| Format | Example | Description |
|--------|---------|-------------|
| **IP Address** | `*************` | Simple IP (default HTTPS) |
| **IP with Port** | `*************:8443` | IP with custom port |
| **Hostname** | `seqrng.example.com` | Domain name (default HTTPS) |
| **Hostname with Port** | `ctm.example.com:443` | Domain with custom port |
| **Full URL** | `https://api.seqrng.com` | Complete URL with protocol |
| **HTTP URL** | `http://*************:8080` | Custom protocol and port |
| **IPv6** | `2001:db8::1` | IPv6 addresses supported |

### Environment Variables
```bash
# SeQRNG Configuration - supports multiple formats
export SEQRNG_IP_ADDRESS="*************"              # IP
# export SEQRNG_IP_ADDRESS="https://seqrng.example.com"  # URL
# export SEQRNG_IP_ADDRESS="seqrng.example.com:8443"    # Hostname:port
export SEQRNG_API_TOKEN="your_token"

# CTM Configuration - supports multiple formats  
export CTM_IP_ADDRESS="*************"                 # IP
# export CTM_IP_ADDRESS="https://ctm.example.com"       # URL
# export CTM_IP_ADDRESS="ctm.example.com:443"           # Hostname:port
export CTM_USERNAME="username"
export CTM_PASSWORD="password"
export CTM_DOMAIN="domain"
```

### .env File
```bash
# Create from template (includes examples of all supported formats)
python config.py --create-examples
cp .env.example .env
# Edit .env with your credentials using any supported format
```

### 🔄 Backward Compatibility

✅ **Existing configurations continue to work unchanged**  
✅ **Automatic format detection** - no migration required  
✅ **Mix and match** - use IP for CTM, URL for SeQRNG, etc.  
✅ **Smart defaults** - HTTPS protocol assumed for security  

**Migration**: No action required! Your existing IP-based configurations will continue working exactly as before.

## 🏗️ Architecture

- **Frontend**: Python 3.7+ application
- **SeQRNG**: Quantum Random Number Generator API
- **CTM**: Thales CipherTrust Manager
- **Security**: Multi-layer configuration management

## 📖 Documentation

- [DEPLOYMENT.md](DEPLOYMENT.md) - Comprehensive deployment guide
- [config.py](config.py) - Configuration management system
- [install.sh](install.sh) - Automated installation script

## 🔧 Features

- **Single & Multiple Key Upload**: Supports both individual and batch key generation
- **Multiple Algorithms**: AES, ARIA, HMAC-SHA1/256/384/512
- **Flexible Connection Formats**: IP addresses, hostnames, full URLs - automatically detected
- **Key Validation**: Automatic validation of key sizes and algorithms
- **Error Handling**: Robust error handling with fallback mechanisms
- **Duplicate Prevention**: Automatic detection and handling of duplicate key names
- **Production Ready**: Systemd service, logging, monitoring
- **Backward Compatible**: Existing configurations work without changes

## 💡 Common Configuration Examples

### Cloud/Production Environment
```bash
# Using full URLs with custom domains
SEQRNG_IP_ADDRESS="https://quantum-api.yourcompany.com"
CTM_IP_ADDRESS="https://ctm.yourcompany.com:443"
```

### Development Environment
```bash
# Using local IPs with custom ports
SEQRNG_IP_ADDRESS="*************:8443"
CTM_IP_ADDRESS="*************:8080"
```

### Hybrid Environment
```bash
# Mix of formats - cloud SeQRNG, local CTM
SEQRNG_IP_ADDRESS="https://seqrng.example.com"
CTM_IP_ADDRESS="*********"
```

### Legacy/Existing Setup
```bash
# Keep your current configuration - no changes needed!
SEQRNG_IP_ADDRESS="**************"
CTM_IP_ADDRESS="*************"
```

## 🛡️ Security Considerations

⚠️ **Important**: This software handles cryptographic keys. Please:

1. Review [DEPLOYMENT.md](DEPLOYMENT.md) for security hardening
2. Use strong, unique credentials
3. Implement network segmentation
4. Enable monitoring and logging
5. Regular security audits

## 📞 Support

For deployment assistance, see [DEPLOYMENT.md](DEPLOYMENT.md) for troubleshooting guides and best practices.
