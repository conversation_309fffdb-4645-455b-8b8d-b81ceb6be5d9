"""
Configuration management for SeQRNG-CTM API
"""

import os
from typing import Dict, Any
from urllib.parse import urlparse


def parse_connection_string(connection_string: str, default_protocol: str = "http") -> str:
    """
    Parse and normalize connection string to full URL
    
    Args:
        connection_string: IP address, hostname, or full URL
        default_protocol: Default protocol if none specified
        
    Returns:
        Normalized URL string
    """
    if not connection_string:
        raise ValueError("Connection string cannot be empty")
    
    # If it already has a protocol, return as-is (with trailing slash removed)
    if connection_string.startswith(('http://', 'https://')):
        return connection_string.rstrip('/')
    
    # If it looks like an IP address or hostname, add the default protocol
    return f"{default_protocol}://{connection_string}"


class Config:
    """Configuration management class"""
    
    def __init__(self, env_file: str = '.env'):
        """
        Initialize configuration
        
        Args:
            env_file: Path to environment file
        """
        self.env_file = env_file
        self._load_env_file()
    
    def _load_env_file(self):
        """Load environment variables from .env file if it exists"""
        if os.path.exists(self.env_file):
            with open(self.env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        # Only set if not already in environment
                        if key not in os.environ:
                            os.environ[key] = value
    
    def get(self, key: str, default: Any = None, required: bool = False) -> Any:
        """
        Get configuration value
        
        Args:
            key: Configuration key
            default: Default value if key not found
            required: Whether the key is required
            
        Returns:
            Configuration value
            
        Raises:
            ValueError: If required key is missing
        """
        value = os.environ.get(key, default)
        if required and value is None:
            raise ValueError(f"Required configuration key '{key}' is missing")
        return value
    
    def validate_config(self) -> bool:
        """
        Validate that all required configuration is present

        Returns:
            True if configuration is valid
        """
        try:
            # Validate SeQRNG configuration - try both old and new variable names
            seqrng_url = self.get('SEQRNG_BASE_URL') or self.get('SEQRNG_IP_ADDRESS')
            if not seqrng_url:
                return False
            self.get('SEQRNG_API_TOKEN', required=True)

            # Validate CTM configuration
            self.get('CTM_IP_ADDRESS', required=True)
            self.get('CTM_USERNAME', required=True)
            self.get('CTM_PASSWORD', required=True)
            self.get('CTM_DOMAIN', required=True)

            return True
        except ValueError:
            return False
    
    def get_seqrng_config(self) -> Dict[str, Any]:
        """Get SeQRNG configuration with normalized URL"""
        # Try both old and new variable names for backward compatibility
        raw_url = self.get('SEQRNG_BASE_URL') or self.get('SEQRNG_IP_ADDRESS')
        if not raw_url:
            raise ValueError("SeQRNG URL/IP address not configured. Set SEQRNG_BASE_URL or SEQRNG_IP_ADDRESS")

        try:
            normalized_url = parse_connection_string(raw_url, default_protocol="https")
            return {
                'base_url': normalized_url,
                'api_token': self.get('SEQRNG_API_TOKEN', required=True)
            }
        except ValueError as e:
            raise ValueError(f"Invalid SeQRNG URL format '{raw_url}': {e}")
    
    def get_ctm_config(self) -> Dict[str, Any]:
        """Get CipherTrust Manager configuration with normalized URL"""
        raw_address = self.get('CTM_IP_ADDRESS', required=True)
        try:
            normalized_url = parse_connection_string(raw_address, default_protocol="https")
            return {
                'ip_address': raw_address,  # Keep original for backward compatibility
                'base_url': normalized_url,  # New normalized URL
                'username': self.get('CTM_USERNAME', required=True),
                'password': self.get('CTM_PASSWORD', required=True),
                'domain': self.get('CTM_DOMAIN', required=True)
            }
        except ValueError as e:
            raise ValueError(f"Invalid CTM address format '{raw_address}': {e}")
