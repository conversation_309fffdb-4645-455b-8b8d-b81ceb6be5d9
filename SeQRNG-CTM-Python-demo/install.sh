#!/bin/bash

# SeQRNG-CTM Installation Script
# This script automates the deployment process for production environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="seqrng-ctm"
APP_DIR="/opt/$APP_NAME"
APP_USER="$APP_NAME"
SERVICE_NAME="$APP_NAME"
PYTHON_VERSION="3.7"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

check_python() {
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is not installed"
        exit 1
    fi
    
    PYTHON_VER=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    if [ "$(echo "$PYTHON_VER < $PYTHON_VERSION" | bc -l)" -eq 1 ]; then
        log_error "Python $PYTHON_VERSION or higher is required. Found: $PYTHON_VER"
        exit 1
    fi
    
    log_success "Python $PYTHON_VER detected"
}

create_user() {
    if ! id "$APP_USER" &>/dev/null; then
        log_info "Creating user $APP_USER..."
        useradd -r -s /bin/false -d "$APP_DIR" "$APP_USER"
        log_success "User $APP_USER created"
    else
        log_info "User $APP_USER already exists"
    fi
}

create_directories() {
    log_info "Creating application directories..."
    mkdir -p "$APP_DIR"
    mkdir -p "$APP_DIR/logs"
    mkdir -p "/etc/$APP_NAME"
    
    log_success "Directories created"
}

install_dependencies() {
    log_info "Installing Python dependencies..."
    
    # Install pip if not present
    if ! command -v pip3 &> /dev/null; then
        log_info "Installing pip..."
        apt-get update
        apt-get install -y python3-pip
    fi
    
    # Install requirements
    pip3 install -r requirements.txt
    
    log_success "Dependencies installed"
}

copy_files() {
    log_info "Copying application files..."
    
    # Copy Python files
    cp *.py "$APP_DIR/"
    cp requirements.txt "$APP_DIR/"
    cp DEPLOYMENT.md "$APP_DIR/"
    
    # Set permissions
    chown -R "$APP_USER:$APP_USER" "$APP_DIR"
    chmod 755 "$APP_DIR"
    chmod 644 "$APP_DIR"/*.py
    chmod 755 "$APP_DIR/config.py"
    
    log_success "Files copied and permissions set"
}

setup_configuration() {
    log_info "Setting up configuration..."
    
    # Create example configuration files
    cd "$APP_DIR"
    sudo -u "$APP_USER" python3 config.py --create-examples
    
    # Move example files to /etc for system-wide access
    if [ -f ".env.example" ]; then
        cp .env.example "/etc/$APP_NAME/env.example"
        chmod 600 "/etc/$APP_NAME/env.example"
        chown root:root "/etc/$APP_NAME/env.example"
    fi
    
    if [ -f "config.json.example" ]; then
        cp config.json.example "/etc/$APP_NAME/config.json.example"
        chmod 600 "/etc/$APP_NAME/config.json.example"  
        chown root:root "/etc/$APP_NAME/config.json.example"
    fi
    
    log_success "Configuration templates created"
}

create_systemd_service() {
    log_info "Creating systemd service..."
    
    cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=SeQRNG CTM Integration Service
After=network.target
Documentation=file://$APP_DIR/DEPLOYMENT.md

[Service]
Type=simple
User=$APP_USER
Group=$APP_USER
WorkingDirectory=$APP_DIR
Environment=PYTHONPATH=$APP_DIR
ExecStart=/usr/bin/python3 $APP_DIR/SeQRNG_CTM_1.4.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# Security settings
NoNewPrivileges=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=$APP_DIR/logs
PrivateTmp=yes
ProtectKernelTunables=yes
ProtectControlGroups=yes
RestrictRealtime=yes
RestrictSUIDSGID=yes

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable "$SERVICE_NAME"
    
    log_success "Systemd service created and enabled"
}

setup_logging() {
    log_info "Setting up logging..."
    
    # Create log rotation configuration
    cat > "/etc/logrotate.d/$APP_NAME" << EOF
$APP_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $APP_USER $APP_USER
    postrotate
        systemctl reload-or-restart $SERVICE_NAME
    endscript
}
EOF

    log_success "Log rotation configured"
}

configure_firewall() {
    if command -v ufw &> /dev/null; then
        log_info "Configuring firewall..."
        
        # This is just an example - adjust ports as needed
        # ufw allow from <trusted_network> to any port 443
        
        log_warning "Firewall configuration needs manual setup based on your network"
        log_info "Please review DEPLOYMENT.md for firewall configuration examples"
    fi
}

show_completion_message() {
    log_success "Installation completed successfully!"
    echo
    echo "🔧 Next steps:"
    echo "1. Configure your credentials:"
    echo "   sudo $APP_DIR/config.py --setup"
    echo "   OR"
    echo "   cp /etc/$APP_NAME/env.example /etc/$APP_NAME/.env"
    echo "   sudo nano /etc/$APP_NAME/.env"
    echo
    echo "2. Test the configuration:"
    echo "   sudo -u $APP_USER python3 -c \"from config import get_config; config=get_config(); config.validate_config()\""
    echo
    echo "3. Start the service:"
    echo "   sudo systemctl start $SERVICE_NAME"
    echo
    echo "4. Check service status:"
    echo "   sudo systemctl status $SERVICE_NAME"
    echo
    echo "5. View logs:"
    echo "   sudo journalctl -u $SERVICE_NAME -f"
    echo
    echo "📖 For detailed information, see: $APP_DIR/DEPLOYMENT.md"
}

# Main installation process
main() {
    log_info "Starting SeQRNG-CTM installation..."
    
    check_root
    check_python
    create_user
    create_directories
    install_dependencies
    copy_files
    setup_configuration
    create_systemd_service
    setup_logging
    configure_firewall
    show_completion_message
}

# Check if running in interactive mode
if [ -t 0 ]; then
    echo "SeQRNG-CTM Installation Script"
    echo "=============================="
    echo
    echo "This script will install SeQRNG-CTM as a system service."
    echo "Installation directory: $APP_DIR"
    echo "Service user: $APP_USER"
    echo
    read -p "Continue with installation? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Installation cancelled."
        exit 1
    fi
fi

# Run main installation
main 